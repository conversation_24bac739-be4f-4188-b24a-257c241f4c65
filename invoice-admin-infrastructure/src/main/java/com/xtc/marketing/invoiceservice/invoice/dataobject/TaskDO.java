package com.xtc.marketing.invoiceservice.invoice.dataobject;

import com.mybatisflex.annotation.Table;
import com.xtc.marketing.invoiceservice.config.BaseDO;
import com.xtc.marketing.invoiceservice.invoice.enums.TaskStatusEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 任务表
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@Table("t_task")
public class TaskDO extends BaseDO {

    /**
     * 任务id（UUID）
     */
    private String taskId;
    /**
     * 日志id (MDC traceId + uuid)
     */
    private String logId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务状态
     */
    private TaskStatusEnum taskStatus;
    /**
     * 请求入参的 json 字符串
     */
    private String requestParamsJson;
    /**
     * 数据总数
     */
    private Long total;
    /**
     * 已处理数量
     */
    private Long processedCount;
    /**
     * 处理进度百分比 = 已同步数量 / 数据总数
     */
    private String progressPercentage;
    /**
     * 文件id
     */
    private String fileId;

}
