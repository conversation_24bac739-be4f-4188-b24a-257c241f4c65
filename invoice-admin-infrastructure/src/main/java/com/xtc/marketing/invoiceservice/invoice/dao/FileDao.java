package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.query.If;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.FileMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.FileDO;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.FileDOTableDef.FILE_DO;

/**
 * 文件数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class FileDao extends BaseDao<FileMapper, FileDO> {

    /**
     * 查询文件
     *
     * @param fileId 文件id
     * @return 文件
     */
    public Optional<FileDO> getByFileId(String fileId) {
        if (If.noText(fileId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(FILE_DO.FILE_ID.eq(fileId))
                .orderBy(FILE_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

}
