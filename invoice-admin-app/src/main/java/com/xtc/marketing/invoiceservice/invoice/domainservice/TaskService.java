package com.xtc.marketing.invoiceservice.invoice.domainservice;

import com.xtc.marketing.invoiceservice.config.BaseDO;
import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.context.TaskContext;
import com.xtc.marketing.invoiceservice.invoice.dao.FileDao;
import com.xtc.marketing.invoiceservice.invoice.dao.TaskDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.FileDO;
import com.xtc.marketing.invoiceservice.invoice.dataobject.TaskDO;
import com.xtc.marketing.invoiceservice.invoice.enums.TaskStatusEnum;
import com.xtc.marketing.invoiceservice.invoice.export.ExportTaskEnum;
import com.xtc.marketing.invoiceservice.oss.BizFileOssClient;
import com.xtc.marketing.invoiceservice.oss.cosntant.BizFileOssConstant;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import com.xtc.marketing.invoiceservice.util.FastExcelUtil;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import com.xtc.marketing.invoiceservice.util.UuidGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 任务领域服务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TaskService {

    /**
     * 任务最大并发数
     */
    private static final int MAX_CONCURRENT = 50;
    /**
     * 默认分片处理数据量
     */
    private static final int DEFAULT_SHARD_SIZE = 50;
    /**
     * 默认分批导出数据量
     */
    private static final int DEFAULT_EXPORT_BATCH_SIZE = 200;
    /**
     * 单个用户最大任务数
     */
    private static final int TASK_PER_USER_LIMIT = 10;
    /**
     * 限制任务处理的数据量
     */
    private static final int TASK_DATA_LIMIT = 1000000;
    /**
     * 任务超时时间
     */
    private static final Duration TASK_TIMEOUT = Duration.ofMinutes(5);

    // infra
    private final TaskDao taskDao;
    private final FileDao fileDao;
    private final BizFileOssClient bizFileOssClient;

    /**
     * 虚拟线程执行器
     */
    private ExecutorService virtualExecutor;

    @PostConstruct
    public void init() {
        // 初始化虚拟线程执行器
        ThreadFactory virtualThreadFactory = Thread.ofVirtual().name("task-", 0).factory();
        virtualExecutor = Executors.newThreadPerTaskExecutor(virtualThreadFactory);
    }

    @PreDestroy
    public void shutdown() {
        // 关闭虚拟线程执行器
        if (virtualExecutor != null && !virtualExecutor.isShutdown()) {
            virtualExecutor.shutdown();
        }
    }

    /**
     * 判断任务已超时
     *
     * @param task 任务
     * @return 执行结果
     */
    public boolean isTaskTimeout(TaskDO task) {
        if (task == null || task.getTaskStatus() == null || task.getUpdateTime() == null) {
            return false;
        }
        if (task.getTaskStatus() == TaskStatusEnum.TIMEOUT) {
            return true;
        }
        if (task.getTaskStatus() != TaskStatusEnum.RUNNING) {
            return false;
        }
        Duration duration = Duration.between(task.getUpdateTime(), LocalDateTime.now());
        return duration.toSeconds() >= TASK_TIMEOUT.toSeconds();
    }

    /**
     * 提交导出任务
     *
     * @param exportTask  导出任务
     * @param request     请求参数
     * @param total       数据总数
     * @param exportClass 导出数据类型
     * @param batchData   批量数据提供函数
     * @param <R>         请求参数类型
     * @param <T>         导出数据类型
     */
    public <R, T> void submitExportTask(ExportTaskEnum exportTask, R request, long total, Class<T> exportClass, Function<T, List<T>> batchData) {
        // 初始化文件路径：/taskName/datePath/userId/taskName_yyyyMMdd_HHmmss_SSS.csv
        String taskName = exportTask.getDesc();
        LocalDateTime now = LocalDateTime.now();
        String datePath = DateUtil.toString(now, DateUtil.FORMAT_DATE_COMPACT);
        String fileName = "%s_%s.csv".formatted(taskName, DateUtil.toString(now, "yyyyMMdd_HHmmss_SSS"));
        String objectName = "%s/%s/%s/%s".formatted(taskName, datePath, UserContext.getUser().getUserId(), fileName);
        String tempFilePath = "logs/export/%s".formatted(objectName);
        // 提交任务
        submitTask(taskName, request, total, DEFAULT_EXPORT_BATCH_SIZE, (taskContext, jobRuntime) -> {
            // 生成临时 excel 文件
            File file = new File(tempFilePath);
            if (!file.getParentFile().exists()) {
                boolean initFolder = file.getParentFile().mkdirs();
                if (!initFolder) {
                    throw BizException.of("创建导出文件目录失败");
                }
            }
            log.info("生成临时 excel 文件 {}", tempFilePath);
            // 提交虚拟线程任务
            virtualExecutor.execute(() -> jobRuntime.jobExecutor(
                    runtime -> {
                        // 更新任务状态为运行中
                        taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.RUNNING);
                        log.info("开始导出任务 {} {}", taskContext.getId(), TaskStatusEnum.RUNNING);
                        // 写入数据到文件
                        FastExcelUtil.writeExcelBatch(
                                file,
                                total,
                                exportClass,
                                lastData -> {
                                    List<T> data = batchData.apply(lastData);
                                    // 增加计数
                                    long syncedCount = runtime.getCounter().addAndGet(data.size());
                                    this.updateTaskProcessedCount(taskContext, syncedCount);
                                    return data;
                                }
                        );
                        // 上传文件到 oss
                        String fullObjectName = BizFileOssConstant.buildExportObjectName(objectName);
                        bizFileOssClient.putObject(fullObjectName, file);
                        // 更新任务文件
                        String fileId = this.saveExportFile(fileName, fullObjectName);
                        taskDao.updateTaskFile(taskContext.getId(), fileId);
                        log.info("导出任务完成，文件已上传 oss 成功 {}", objectName);
                        // 删成临时 excel 文件
                        if (file.exists()) {
                            try {
                                Files.delete(Path.of(tempFilePath));
                                log.info("删除临时文件成功 {}", tempFilePath);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败 {}", tempFilePath, e);
                            }
                        }
                    }
            ));
        });
    }

    /**
     * 提交分片任务
     *
     * @param taskName         任务名称
     * @param request          请求参数
     * @param total            数据总数
     * @param pageDataSupplier 分页数据提供函数
     * @param dataConsumer     数据消费函数
     * @param <R>              请求参数类型
     * @param <T>              数据类型
     */
    public <R, T> void submitShardTask(String taskName, R request, long total, Function<TaskContext<R>, List<T>> pageDataSupplier, Consumer<List<T>> dataConsumer) {
        submitTask(taskName, request, total, DEFAULT_SHARD_SIZE, (taskContext, jobRuntime) -> {
            // 遍历所有页同步数据
            for (int index = 1; index <= taskContext.getPages(); index++) {
                // 设置任务上下文页码
                taskContext.setPageIndex(index);
                // 提交虚拟线程任务
                virtualExecutor.execute(() -> jobRuntime.jobExecutor(
                        runtime -> {
                            taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.RUNNING);
                            // 获取分页数据
                            List<T> data = pageDataSupplier.apply(taskContext);
                            // 增加计数
                            long syncedCount = runtime.getCounter().addAndGet(data.size());
                            this.updateTaskProcessedCount(taskContext, syncedCount);
                            // 处理数据
                            dataConsumer.accept(data);
                        }
                ));
            }
        });
    }

    /**
     * 提交任务
     *
     * @param taskName   任务名称
     * @param request    请求参数
     * @param total      数据总数
     * @param pageSize   分页数据量
     * @param jobExecute 任务执行函数
     * @param <R>        请求参数类型
     */
    public <R> void submitTask(String taskName, R request, long total, int pageSize, BiConsumer<TaskContext<R>, JobRuntime> jobExecute) {
        // 检查允许提交任务
        this.checkSubmitTask(total);
        // 创建任务上下文
        TaskContext<R> taskContext = this.createTaskContext(request, total, pageSize);
        // 数据总数为 0 则不需要处理，直接完成任务
        if (taskContext.getTotal() <= 0) {
            throw BizException.of("任务没有数据需要处理");
        }
        // 创建任务
        TaskDO task = this.createTask(taskName, taskContext);
        taskContext.setId(task.getId());
        log.info("开始任务处理 {}", taskContext);
        try {
            // 初始化任务运行时，设置任务处理并发数
            JobRuntime jobRuntime = new JobRuntime(MAX_CONCURRENT);
            // 提交虚拟线程任务
            jobExecute.accept(taskContext, jobRuntime);
        } catch (Exception e) {
            log.error("任务失败 {}", taskContext.getId(), e);
            taskDao.updateTaskStatus(taskContext.getId(), TaskStatusEnum.FAILED);
        }
    }

    /**
     * 检查允许提交任务
     *
     * @param total 数据总数
     */
    private void checkSubmitTask(long total) {
        if (total > TASK_DATA_LIMIT) {
            throw BizException.of("任务数据量超过限制，请拆分任务 [最大允许 %s 条数据]", TASK_DATA_LIMIT);
        }
        UserContext.User user = UserContext.getUser();
        if (user != null) {
            boolean isTaskCountExceedLimit = taskDao.isTaskCountExceedLimit(user.getEmployeeId(), TASK_PER_USER_LIMIT);
            if (isTaskCountExceedLimit) {
                throw BizException.of("您有任务正在处理中，请等待任务完成后再提交新任务");
            }
        }
    }

    /**
     * 保存导出文件
     *
     * @param fileName   文件名
     * @param objectName 对象名
     * @return 文件id
     */
    private String saveExportFile(String fileName, String objectName) {
        String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
        FileDO file = FileDO.builder()
                .fileName(fileName)
                .objectName(objectName)
                .fileType(fileType)
                .build();
        fileDao.save(file);
        return file.getFileId();
    }

    /**
     * 更新任务已处理数量
     *
     * @param taskContext    任务上下文
     * @param processedCount 已处理数量
     */
    private void updateTaskProcessedCount(TaskContext<?> taskContext, long processedCount) {
        if (processedCount <= 0 || taskContext.getTotal() <= 0) {
            return;
        }
        TaskDO updateTask = new TaskDO();
        updateTask.setProcessedCount(processedCount);
        // 计算同步进度百分比
        long progressPercentage = Math.round(processedCount * 100.0 / taskContext.getTotal());
        updateTask.setProgressPercentage(progressPercentage + "%");
        // 判断同步完成
        if (processedCount == taskContext.getTotal()) {
            updateTask.setTaskStatus(TaskStatusEnum.COMPLETED);
        }
        taskDao.updateById(taskContext.getId(), updateTask);
        log.info("更新任务已处理数量 {} {} {}", updateTask.getProcessedCount(), updateTask.getProgressPercentage(),
                updateTask.getTaskStatus() != null ? updateTask.getTaskStatus() : StringUtils.EMPTY);
    }

    /**
     * 创建任务
     *
     * @param taskName    任务名称
     * @param taskContext 任务上下文
     * @param <R>         请求参数类型
     * @return 任务
     */
    private <R> TaskDO createTask(String taskName, TaskContext<R> taskContext) {
        // 创建任务
        TaskDO task = new TaskDO();
        task.setTaskName(taskName);
        task.setTaskStatus(TaskStatusEnum.PENDING);
        task.setRequestParamsJson(GsonUtil.objectToJson(taskContext.getRequest()));
        task.setTotal(taskContext.getTotal());
        task.setProcessedCount(0L);
        task.setProgressPercentage("0%");
        // 设置时间
        LocalDateTime now = LocalDateTime.now();
        task.setCreateTime(now);
        task.setUpdateTime(LocalDateTime.now());
        // 设置任务id
        String taskId = UuidGenerator.randomUuid();
        task.setTaskId(taskId);
        // 设置日志id
        String traceId = StringUtils.defaultIfBlank(MDC.get(SystemConstant.MDC_TRACE_ID), taskId);
        task.setLogId(traceId);
        // 数据总数为 0 则不需要处理，直接完成任务
        if (taskContext.getTotal() <= 0) {
            task.setTaskStatus(TaskStatusEnum.COMPLETED);
            task.setProgressPercentage("100%");
        }
        // 设置创建人
        UserContext.User user = UserContext.getUser();
        BaseDO.Operator creator = user != null ? BaseDO.Operator.of(user.getEmployeeId(), user.getUserName()) : BaseDO.Operator.ofSystem();
        task.setCreateBy(creator);
        // 保存数据
        taskDao.save(task);
        return task;
    }

    /**
     * 创建任务上下文
     *
     * @param request  请求参数
     * @param total    数据总数
     * @param pageSize 分页数据量
     * @param <R>      请求参数类型
     * @return 任务上下文
     */
    private <R> TaskContext<R> createTaskContext(R request, long total, int pageSize) {
        TaskContext<R> taskContext = TaskContext.<R>builder()
                .total(total)
                .pages(0)
                .pageSize(pageSize)
                .pageIndex(0)
                .request(request)
                .build();
        if (taskContext.getTotal() <= 0) {
            return taskContext;
        }
        // 计算总页数
        int pages = (int) (taskContext.getTotal() / taskContext.getPageSize());
        // 如果总数不能被分页大小整除，则需要多一页
        if (taskContext.getTotal() % taskContext.getPageSize() > 0) {
            pages++;
        }
        taskContext.setPages(pages);
        return taskContext;
    }

    /**
     * 任务运行时
     */
    @Getter
    @Setter
    @ToString
    public static class JobRuntime {

        /**
         * 全链路跟踪id
         */
        private String traceId;
        /**
         * 并发信号量
         */
        private Semaphore semaphore;
        /**
         * 计数器
         */
        private AtomicLong counter;

        /**
         * 生成任务运行时
         *
         * @param maxConcurrent 最大并发数
         */
        JobRuntime(int maxConcurrent) {
            this.traceId = MDC.get(SystemConstant.MDC_TRACE_ID);
            this.semaphore = new Semaphore(maxConcurrent);
            this.counter = new AtomicLong(0);
        }

        /**
         * 任务执行器
         *
         * @param syncDataJob 同步数据函数
         */
        private void jobExecutor(Consumer<JobRuntime> syncDataJob) {
            boolean acquireSemaphore = false;
            // 设置虚拟线程执行id
            String executeId = UuidGenerator.randomUuid();
            try (MDC.MDCCloseable ignored = MDC.putCloseable(SystemConstant.MDC_EXECUTE_ID, executeId);
                 MDC.MDCCloseable ignored2 = MDC.putCloseable(SystemConstant.MDC_TRACE_ID, this.traceId)) {
                // 获取信号量
                acquireSemaphore = this.acquireSemaphore();
                // 处理数据
                if (acquireSemaphore) {
                    try {
                        // 执行同步数据函数
                        syncDataJob.accept(this);
                    } catch (Exception e) {
                        log.warn("同步数据失败 message: {}", e.getMessage(), e);
                    }
                }
            } finally {
                // 释放信号量
                if (acquireSemaphore) {
                    this.releaseSemaphore();
                }
            }
        }

        /**
         * 获取信号量
         *
         * @return 执行结果
         */
        public boolean acquireSemaphore() {
            try {
                this.semaphore.acquire();
                return true;
            } catch (InterruptedException e) {
                log.warn("获取信号量许可被中断", e);
                Thread.currentThread().interrupt();
                return false;
            }
        }

        /**
         * 释放信号量
         */
        public void releaseSemaphore() {
            this.semaphore.release();
        }

    }

}
