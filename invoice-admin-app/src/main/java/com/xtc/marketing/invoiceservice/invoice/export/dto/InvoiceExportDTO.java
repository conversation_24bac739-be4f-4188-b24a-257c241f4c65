package com.xtc.marketing.invoiceservice.invoice.export.dto;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceBizTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.InvoiceTypeEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.PlatformCodeEnum;
import com.xtc.marketing.invoiceservice.invoice.export.convert.StringEnumConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 发票导出 DTO
 */
@Getter
@Setter
@ToString
public class InvoiceExportDTO {

    /**
     * 唯一标识
     */
    @ExcelIgnore
    private Long id;
    /**
     * 业务代码
     */
    @ExcelProperty("业务代码")
    private String bizCode;
    /**
     * 业务订单编号
     */
    @ExcelProperty("业务订单编号")
    private String bizOrderId;
    /**
     * 平台代码
     */
    @ExcelProperty(value = "平台代码", converter = StringEnumConverter.class)
    private PlatformCodeEnum platformCode;
    /**
     * 开票流水号
     */
    @ExcelProperty("开票流水号")
    private String serialNo;
    /**
     * 蓝票号码
     */
    @ExcelProperty("蓝票号码")
    private String blueInvoiceNo;
    /**
     * 红票号码
     */
    @ExcelProperty("红票号码")
    private String redInvoiceNo;
    /**
     * 开票类型
     */
    @ExcelProperty(value = "开票类型", converter = StringEnumConverter.class)
    private CreateTypeEnum createType;
    /**
     * 发票类型
     */
    @ExcelProperty(value = "发票类型", converter = StringEnumConverter.class)
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票业务类型
     */
    @ExcelProperty(value = "发票业务类型", converter = StringEnumConverter.class)
    private InvoiceBizTypeEnum invoiceBizType;
    /**
     * 开票时间
     */
    @ExcelProperty("开票时间")
    private LocalDateTime invoiceTime;
    /**
     * 开票金额
     */
    @ExcelProperty("开票金额")
    private Integer invoiceAmount;
    /**
     * 合计税额
     */
    @ExcelProperty("合计税额")
    private Integer taxAmount;
    /**
     * 合计不含税总金额
     */
    @ExcelProperty("合计不含税总金额")
    private Integer priceAmount;
    /**
     * 发票抬头
     */
    @ExcelProperty("发票抬头")
    private String invoiceTitle;
    /**
     * 购买方税号
     */
    @ExcelProperty("购买方税号")
    private String buyerIdentifyNo;
    /**
     * 购买方电话
     */
    @ExcelProperty("购买方电话")
    private String buyerPhone;
    /**
     * 购买方地址
     */
    @ExcelProperty("购买方地址")
    private String buyerAddress;
    /**
     * 购买方银行
     */
    @ExcelProperty("购买方银行")
    private String buyerBankName;
    /**
     * 购买方银行账号
     */
    @ExcelProperty("购买方银行账号")
    private String buyerBankAccount;
    /**
     * 开票人
     */
    @ExcelProperty("开票人")
    private String operator;
    /**
     * 销售方税号
     */
    @ExcelProperty("销售方税号")
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    @ExcelProperty("销售方名称")
    private String sellerName;
    /**
     * 销售方电话
     */
    @ExcelProperty("销售方电话")
    private String sellerPhone;
    /**
     * 销售方地址
     */
    @ExcelProperty("销售方地址")
    private String sellerAddress;
    /**
     * 销售方银行
     */
    @ExcelProperty("销售方银行")
    private String sellerBankName;
    /**
     * 销售方银行账号
     */
    @ExcelProperty("销售方银行账号")
    private String sellerBankAccount;
    /**
     * 冲红原因
     */
    @ExcelProperty("冲红原因")
    private String redReason;

}
