<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xtc.marketing.invoiceservice</groupId>
        <artifactId>invoice-admin-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>invoice-admin-app</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--Project modules-->
        <dependency>
            <groupId>com.xtc.marketing.invoiceservice</groupId>
            <artifactId>invoice-admin-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xtc.marketing.invoiceservice</groupId>
            <artifactId>invoice-admin-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xtc.marketing.invoiceservice</groupId>
            <artifactId>invoice-admin-common</artifactId>
        </dependency>
        <!--Project modules End-->

        <!--XTC Client-->
        <dependency>
            <groupId>com.xtc.marketing.invoiceservice</groupId>
            <artifactId>invoice-service-client</artifactId>
        </dependency>
        <!--XTC Client End-->

        <!--Misc-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <!--Misc End-->
    </dependencies>
</project>
