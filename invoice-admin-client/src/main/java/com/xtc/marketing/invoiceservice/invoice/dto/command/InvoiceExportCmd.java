package com.xtc.marketing.invoiceservice.invoice.dto.command;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 发票导出参数
 */
@Getter
@Setter
@ToString
public class InvoiceExportCmd {

    /**
     * 开票时间开始
     */
    @NotNull
    private LocalDate invoiceDateStart;
    /**
     * 开票时间结束
     */
    @NotNull
    private LocalDate invoiceDateEnd;

}
