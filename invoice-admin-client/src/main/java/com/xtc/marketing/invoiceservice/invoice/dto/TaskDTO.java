package com.xtc.marketing.invoiceservice.invoice.dto;

import com.xtc.marketing.invoiceservice.invoice.enums.TaskStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 任务DTO
 */
@Getter
@Setter
@ToString
public class TaskDTO {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 日志id
     */
    private String logId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务状态
     *
     * @see TaskStatusEnum
     */
    private String taskStatus;
    /**
     * 请求入参的 json 字符串
     */
    private String requestParamsJson;
    /**
     * 数据总数
     */
    private Long total;
    /**
     * 已处理数量
     */
    private Long processedCount;
    /**
     * 处理进度百分比 = 已同步数量 / 数据总数
     */
    private String progressPercentage;
    /**
     * 文件id
     */
    private String fileId;
    /**
     * 创建人
     */
    private BaseDTO.OperatorDTO createBy;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}