package com.xtc.marketing.invoiceservice.util;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.UrlJwkProvider;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.Verification;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.management.openmbean.InvalidKeyException;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * JWT工具类
 */
@Slf4j
public class JwtUtil {

    /**
     * JWT存储在请求头headers里的名称
     */
    private static final String JWT_HEADER = "Authorization";
    /**
     * JWT存储在请求头headers里的值的前缀
     */
    private static final String JWT_PREFIX = "Bearer ";
    /**
     * 默认时区偏移量：UTC+8（时间相关数据使用）
     */
    private static final ZoneOffset DEFAULT_ZONE_OFFSET = ZoneOffset.of("+8");
    /**
     * RSA算法
     */
    private static final String ALGORITHM_RSA = "RSA";
    /**
     * RSA 密钥大小，通常为 2048 或 4096
     */
    private static final int RAS_KEY_SIZE = 2048;
    /**
     * jwks url
     */
    private static final String JWKS_URL = "%s/protocol/openid-connect/certs";
    /**
     * claim：系统
     */
    private static final String CLAIM_SYSTEM = "azp";
    /**
     * claim：工号
     */
    private static final String CLAIM_USER_USERNAME = "preferred_username";
    /**
     * JWT算法缓存
     */
    private static final ConcurrentMap<String, Algorithm> ALGORITHM_CACHE = new ConcurrentHashMap<>();

    /* 本地缓存数据 */
    /**
     * 公钥
     */
    private static RSAPublicKey rsaPublicKey;
    /**
     * 私钥
     */
    private static RSAPrivateKey rsaPrivateKey;

    private JwtUtil() {
    }

    /**
     * 获取请求头headers里的jwt字符串
     *
     * @param request 请求
     * @return jwt字符串
     */
    public static Optional<String> getJwt(HttpServletRequest request) {
        Enumeration<String> requestHeader = request.getHeaderNames();
        while (requestHeader.hasMoreElements()) {
            String headerKey = requestHeader.nextElement();
            if (JWT_HEADER.equalsIgnoreCase(headerKey)) {
                String jwt = request.getHeader(headerKey);
                if (StringUtils.isNotBlank(jwt)) {
                    jwt = jwt.replace(JWT_PREFIX, "");
                    return StringUtils.isNotBlank(jwt) ? Optional.of(jwt) : Optional.empty();
                }
            }
        }
        return Optional.empty();
    }

    /**
     * 验证 jwt 并获取 preferred_username
     *
     * @param jwt    jwt
     * @param issuer jwt 发行方
     * @param system claim：系统
     * @return 用户id
     */
    public static Optional<String> verifyAndGetUsername(String jwt, String issuer, String system) {
        Map<String, Claim> claims = verify(jwt, issuer, system);
        Claim username = claims.get(CLAIM_USER_USERNAME);
        return Optional.ofNullable(username).map(Claim::asString);
    }

    /**
     * 验证 jwt
     *
     * @param jwt    jwt
     * @param issuer jwt 发行方
     * @param system claim：系统`
     * @return jwt claims
     */
    public static Map<String, Claim> verify(String jwt, String issuer, String system) {
        String jwksUrl = JWKS_URL.formatted(issuer);
        Optional<Algorithm> algorithmOpt = getAlgorithm(jwksUrl, jwt);
        if (algorithmOpt.isEmpty()) {
            return Map.of();
        }
        Verification verification = JWT.require(algorithmOpt.get())
                .withIssuer(issuer)
                .withClaimPresence(CLAIM_USER_USERNAME);
        if (system != null) {
            verification.withClaim(CLAIM_SYSTEM, system);
        }
        return verification.build().verify(jwt).getClaims();
    }

    /**
     * 生成 jwt 令牌
     *
     * @param base64PrivateKey 私钥 base64 字符串
     * @param issuer           发行方
     * @param system           系统
     * @param expiresAt        过期时间
     * @param claims           声明列表
     * @return jwt 令牌
     */
    public static String generateToken(String base64PrivateKey, String issuer, String system, Duration expiresAt, Map<String, String> claims)
            throws NoSuchAlgorithmException, InvalidKeySpecException {
        RSAPrivateKey rsaPrivateKey = getRsaPrivateKey(base64PrivateKey);
        Algorithm algorithm = Algorithm.RSA256(null, rsaPrivateKey);
        LocalDateTime now = LocalDateTime.now();
        return JWT.create()
                .withIssuer(issuer)
                .withClaim(CLAIM_SYSTEM, system)
                .withPayload(claims)
                .withExpiresAt(now.plus(expiresAt).toInstant(DEFAULT_ZONE_OFFSET))
                .withIssuedAt(now.toInstant(DEFAULT_ZONE_OFFSET))
                .withJWTId(UUID.randomUUID().toString())
                .sign(algorithm);
    }

    /**
     * 验证 jwt 并获取声明列表
     *
     * @param base64PublicKey 公钥 base64 字符串
     * @param issuer          发行方
     * @param system          系统
     * @param jwt             jwt 令牌
     * @return 声明列表
     */
    public static Map<String, Claim> verifyAndGetClaims(String base64PublicKey, String issuer, String system, String jwt)
            throws NoSuchAlgorithmException, InvalidKeySpecException {
        RSAPublicKey publicKey = getRsaPublicKey(base64PublicKey);
        Algorithm algorithm = Algorithm.RSA256(publicKey, null);
        Verification verification = JWT.require(algorithm)
                .withIssuer(issuer);
        if (system != null) {
            verification.withClaim(CLAIM_SYSTEM, system);
        }
        return verification.build().verify(jwt).getClaims();
    }

    /**
     * 获取 jwt 签名的算法
     *
     * @param jwksUrl jwks url
     * @param jwt     jwt
     * @return jwt 签名的算法
     */
    private static Optional<Algorithm> getAlgorithm(String jwksUrl, String jwt) {
        String keyId = decodeKeyId(jwt);
        // 读取缓存的算法，不存在则生成新算法，并存入缓存
        Algorithm algorithm = ALGORITHM_CACHE.computeIfAbsent(
                keyId,
                key -> {
                    RSAPublicKey rsaPublicKey;
                    try {
                        rsaPublicKey = getRsaPublicKey(jwksUrl, keyId);
                    } catch (MalformedURLException | JwkException | URISyntaxException e) {
                        log.warn("JwtUtil.generateRSAPublicKey throw exception message: {}", e.getMessage(), e);
                        return null;
                    }
                    return Algorithm.RSA256(rsaPublicKey);
                }
        );
        return Optional.ofNullable(algorithm);
    }

    /**
     * 获取 RSA 公钥
     *
     * @param jwksUrl jwks url
     * @param keyId   jwt keyId
     * @return RSA 公钥
     * @throws MalformedURLException URL 格式错误异常
     * @throws JwkException          jwk 异常
     */
    private static RSAPublicKey getRsaPublicKey(String jwksUrl, String keyId) throws URISyntaxException, MalformedURLException, JwkException {
        URL url = new URI(jwksUrl).toURL();
        JwkProvider jwkProvider = new UrlJwkProvider(url);
        Jwk jwk = jwkProvider.get(keyId);
        return (RSAPublicKey) jwk.getPublicKey();
    }

    /**
     * 获取 RSA 公钥
     *
     * @param base64PublicKey 公钥 base64 字符串
     * @return RSA 公钥
     */
    private static RSAPublicKey getRsaPublicKey(String base64PublicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (rsaPublicKey == null) {
            rsaPublicKey = loadRsaPublicKey(base64PublicKey);
        }
        if (rsaPublicKey == null) {
            throw new InvalidKeyException("Failed to load RSA public key");
        }
        return rsaPublicKey;
    }

    /**
     * 获取 RSA 私钥
     *
     * @param base64PrivateKey 私钥 base64 字符串
     * @return RSA 私钥
     */
    private static RSAPrivateKey getRsaPrivateKey(String base64PrivateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (rsaPrivateKey == null) {
            rsaPrivateKey = loadRsaPrivateKey(base64PrivateKey);
        }
        if (rsaPrivateKey == null) {
            throw new InvalidKeyException("Failed to load RSA private key");
        }
        return rsaPrivateKey;
    }

    /**
     * 生成 RSA 密钥对
     *
     * @return RSA密钥对
     * @throws NoSuchAlgorithmException 如果RSA算法不可用
     */
    private static KeyPair generateRSAKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM_RSA);
        keyPairGenerator.initialize(RAS_KEY_SIZE);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 加载 RSA 公钥
     *
     * @param base64PublicKey 公钥 base64 字符串
     * @return RSA公钥
     */
    private static RSAPublicKey loadRsaPublicKey(String base64PublicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] decoded = Base64.getDecoder().decode(base64PublicKey);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decoded);
        return (RSAPublicKey) keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载 RSA 私钥
     *
     * @param base64PrivateKey 私钥 base64 字符串
     * @return RSA私钥
     */
    private static RSAPrivateKey loadRsaPrivateKey(String base64PrivateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] decoded = Base64.getDecoder().decode(base64PrivateKey);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decoded);
        return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
    }

    /**
     * 从 jwt 解码 keyId
     *
     * @param jwt jwt
     * @return keyId
     */
    private static String decodeKeyId(String jwt) {
        DecodedJWT decodedJWT = JWT.decode(jwt);
        return decodedJWT.getKeyId();
    }

    /**
     * 生成密钥的 base64 字符串
     *
     * @param key 密钥（公钥、密钥）
     */
    private static String generateBase64Key(Key key) {
        byte[] encoded = key.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    public static void main(String[] args) {
        // 测试 sso 验证
        Runnable testSsoVerify = () -> {
            String issuer = "https://sso-test.okii.com/auth/realms/SSO";
            String system = "hotline-center";
            String jwt = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJzZEtiLVNZdnpkckNVU3RkX3VlalVydVpUd0FRR1ZjS3c4RVRkY2stTEZBIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NuY2zlzQtowGUpQhh_ffudOl0u4iSybW1v3BTiuRV8wnBVT3injaG-TVwAEwqi2mmfmV3ppj2DK5pLmZrXB7l4-Yk8Zv2_yOYJ5xtNUdrtM1pnAVvNMQVHWcdyY1NmfdjzTmC5N_OUaRhttHwy4Whrm1oJ9Sr2bmA38tKAHXAwha8csgxI15KcjuHBFUpcwKxqkpEVaEnK6tBpHRN8Y3yjNv9PRdSyTaFF1gW6B93LIxpSqa17rm6LEO4SE6_c4PUG1uDEWxeM5pGO90oW89_t9u2TkhmqBHtWwmHbV2ovm6EgzB0CywoYvEaf_vS6EY1rvgRBQhfXYS4ONsh_vtOQ";
            Optional<String> accountId = JwtUtil.verifyAndGetUsername(jwt, issuer, system);
            log.info("{}", accountId.orElse(null));
            String issuerProd = "https://sso.okii.com/auth/realms/SSO";
            String expireTokenProd = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIwTUEta0RaQmJSbFZKamRRZGhnUHZkbjVwUHZhZ0tKTXlVcndLYmt3M09JIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H7O7kvoezp9OTlopK6UozOUGSRSTBt6QiLfZI4_7ypU9asoJUMVuBBHwDH8JM4OPplYVuiwfkRL98-z77Som8eMooCmNgJ-EBapFNhe2hJrgS_gQEqUs00RurQXrlCKcIm0PAL-TFlDZ1diGm-io0-twWvFKQ6bQA5O78q9VyOE-bkI9C-bgUs8SbuCDtRtt3bRL-g3FFnxIp1aToaqHiLoav9V32oxEXK8OAaGgUXM_EVvbo6X64K0TAUG2WZNKbzl3NlcnkVts3fofy_CX4OFIg8T8tvSDYZBRQZ5qZx9jKfnbvuypiGS2DqnQH22B2VDf4-splt53-Chqq00A0g";
            Optional<String> expireTokenAccountId = JwtUtil.verifyAndGetUsername(expireTokenProd, issuerProd, system);
            log.info("{}", expireTokenAccountId.orElse(null));
        };
        try {
            testSsoVerify.run();
        } catch (Exception e) {
            log.error("testSsoVerify error: {}", e.getMessage(), e);
        }
        // 测试 jwt 生成和验证
        Runnable testJwt = () -> {
            try {
                String issuer = "xtc";
                String system = "system";
                String userId = "modefang";
                // 生成密钥对
                KeyPair keyPair = generateRSAKeyPair();
                // 生成密钥对的 base64 字符串
                String base64PublicKey = generateBase64Key(keyPair.getPublic());
                log.info("-----publicKey-----\n{}", base64PublicKey);
                String base64PrivateKey = generateBase64Key(keyPair.getPrivate());
                log.info("-----privateKey-----\n{}", base64PrivateKey);
                // 生成JWT令牌
                String jwt = generateToken(base64PrivateKey, issuer, system, Duration.ofSeconds(5), Map.of("userId", userId));
                log.info("-----jwt-----\n{}", jwt);
                // 验证JWT令牌
                Map<String, Claim> claims = verifyAndGetClaims(base64PublicKey, issuer, system, jwt);
                log.info("claims: {}", claims);
                // JWT过期验证
                Thread.sleep(6000);
                verifyAndGetClaims(base64PublicKey, issuer, system, jwt);
            } catch (TokenExpiredException e) {
                log.warn("token 已过期", e);
            } catch (Exception e) {
                e.getStackTrace();
                Thread.currentThread().interrupt();
            }
        };
        try {
            testJwt.run();
        } catch (Exception e) {
            log.error("testJwt error: {}", e.getMessage(), e);
        }
    }

}
