package com.xtc.marketing.invoiceservice.constant;

/**
 * 系统常量
 */
public class SystemConstant {

    private SystemConstant() {
    }

    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "invoice-admin";
    /**
     * 测试环境配置
     */
    public static final String PROFILE_TEST = "dev|test";
    /**
     * 系统域名：测试环境
     */
    public static final String SYSTEM_DOMAIN_TEST = "https://marketing-invoice-test.okii.com/admin";
    /**
     * 系统域名：正式环境
     */
    public static final String SYSTEM_DOMAIN_PROD = "https://marketing-invoice-admin.okii.com";
    /**
     * 日志打印key：全链路跟踪id
     */
    public static final String MDC_TRACE_ID = "trace.id";
    /**
     * 日志打印key：执行id（默认用在多线程场景，标识当前线程的执行id）
     */
    public static final String MDC_EXECUTE_ID = "executeId";

    /**
     * 判断测试环境
     *
     * @param profileActive 激活的配置
     * @return 执行结果
     */
    public static boolean isTestProfile(String profileActive) {
        return SystemConstant.PROFILE_TEST.contains(profileActive);
    }

    /**
     * 获取系统域名
     *
     * @param profileActive 环境名
     * @return 系统域名
     */
    public static String getSystemDomain(String profileActive) {
        return isTestProfile(profileActive) ? SYSTEM_DOMAIN_TEST : SYSTEM_DOMAIN_PROD;
    }

}
