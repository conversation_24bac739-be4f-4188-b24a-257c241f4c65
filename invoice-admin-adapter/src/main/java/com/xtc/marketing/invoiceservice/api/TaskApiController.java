package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.annotation.AllowAnonymous;
import com.xtc.marketing.invoiceservice.invoice.TaskAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.TaskDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.GoodsExportCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceExportCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.TaskPageQry;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 任务接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class TaskApiController {

    private final TaskAppService taskAppService;

    /**
     * 任务分页列表
     *
     * @param qry 参数
     * @return 任务分页列表
     */
    @GetMapping("/tasks")
    public PageResponse<TaskDTO> pageTasks(@Valid TaskPageQry qry) {
        return taskAppService.pageTasks(qry);
    }

    /**
     * 生成任务文件链接
     *
     * @param taskId 任务id
     * @return 任务文件链接
     */
    @GetMapping("/task/create-file-url")
    public SingleResponse<String> createFileUrl(@NotBlank @Length(max = 50) @RequestParam("taskId") String taskId) {
        String fileUrl = taskAppService.createFileUrl(taskId);
        return SingleResponse.of(fileUrl);
    }

    /**
     * 下载任务文件
     *
     * @param fileToken 文件凭证
     * @return 任务文件
     */
    @AllowAnonymous
    @GetMapping("/task/download-file")
    public ResponseEntity<Resource> downloadTaskFile(@NotBlank @Length(max = 2000) @RequestParam("fileToken") String fileToken) {
        return taskAppService.downloadTaskFile(fileToken);
    }

    /**
     * 发票导出任务
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/task/export-invoice")
    public Response exportInvoice(@Valid @RequestBody InvoiceExportCmd cmd) {
        taskAppService.exportInvoice(cmd);
        return Response.buildSuccess();
    }

    /**
     * 商品导出任务
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/task/export-goods")
    public Response exportGoods(@Valid @RequestBody GoodsExportCmd cmd) {
        taskAppService.exportGoods(cmd);
        return Response.buildSuccess();
    }

}
